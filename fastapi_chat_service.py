"""
FastAPI service wrapper for chat_backend function
Provides REST API endpoints for chat functionality
"""

import asyncio
import json
from typing import List, Optional, Dict, Any, Union
from fastapi import FastAP<PERSON>, HTTPException, Request
from fastapi.responses import StreamingResponse, JSONResponse
from pydantic import BaseModel, Field
import uvicorn
try:
    from .chat_backend import chat_backend
except ImportError:
    from chat_backend import chat_backend


# Pydantic models for request/response
class Message(BaseModel):
    type: str = Field(..., description="Message type: 'text' or 'image'")
    content: str = Field(..., description="Message content")
    role: str = Field(..., description="Message role: 'user', 'assistant', 'system'")


class ChatRequest(BaseModel):
    messages: List[Message] = Field(..., description="List of messages in the conversation")
    model_name: str = Field(..., description="Name of the model to use")
    model_type: str = Field(..., description="Type of model: 'ollama', 'openai'")
    api_key: Optional[str] = Field(None, description="API key for model authentication")
    openai_base_url: str = Field(..., description="Base URL for the model API")
    stream: bool = Field(True, description="Whether to stream the response")
    mcp_url_list: List[str] = Field(default=[], description="List of MCP server URLs")
    team_id: Optional[int] = Field(None, description="Team ID for team-based chat")


class ChatResponse(BaseModel):
    type: str = Field(..., description="Response type")
    content: str = Field(..., description="Response content")
    source: Optional[str] = Field(None, description="Response source")


# Initialize FastAPI app
app = FastAPI(
    title="AutoGen Studio Chat API",
    description="FastAPI wrapper for AutoGen Studio chat backend",
    version="1.0.0"
)


@app.get("/")
async def root():
    """Root endpoint with API information"""
    return {
        "message": "AutoGen Studio Chat API",
        "version": "1.0.0",
        "endpoints": {
            "chat": "/chat",
            "chat_stream": "/chat/stream",
            "health": "/health"
        }
    }


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "autogen-studio-chat-api"}


@app.post("/chat")
async def chat_endpoint(request: ChatRequest):
    """
    Non-streaming chat endpoint
    
    Args:
        request: ChatRequest containing messages and configuration
        
    Returns:
        ChatResponse with the model's response
    """
    try:
        # Convert Pydantic messages to dict format expected by chat_backend
        messages_dict = [
            {
                "type": msg.type,
                "content": msg.content,
                "role": msg.role
            }
            for msg in request.messages
        ]
        
        # Call chat_backend with stream=False
        result = await chat_backend(
            messages=messages_dict,
            model_name=request.model_name,
            model_type=request.model_type,
            api_key=request.api_key,
            openai_base_url=request.openai_base_url,
            stream=False,
            MCP_url_list=request.mcp_url_list,
            team_id=request.team_id
        )
        
        if isinstance(result, dict):
            return ChatResponse(
                type=result.get("type", "result"),
                content=str(result.get("content", "")),
                source=result.get("source")
            )
        else:
            return ChatResponse(
                type="result",
                content=str(result),
                source=None
            )
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Chat processing failed: {str(e)}")


@app.post("/chat/stream")
async def chat_stream_endpoint(request: ChatRequest):
    """
    Streaming chat endpoint
    
    Args:
        request: ChatRequest containing messages and configuration
        
    Returns:
        StreamingResponse with Server-Sent Events
    """
    try:
        # Convert Pydantic messages to dict format expected by chat_backend
        messages_dict = [
            {
                "type": msg.type,
                "content": msg.content,
                "role": msg.role
            }
            for msg in request.messages
        ]
        
        async def generate_stream():
            """Generator function for streaming response"""
            try:
                # Call chat_backend with stream=True
                async for chunk in chat_backend(
                    messages=messages_dict,
                    model_name=request.model_name,
                    model_type=request.model_type,
                    api_key=request.api_key,
                    openai_base_url=request.openai_base_url,
                    stream=True,
                    MCP_url_list=request.mcp_url_list,
                    team_id=request.team_id
                ):
                    # Format as Server-Sent Events
                    if isinstance(chunk, dict):
                        data = json.dumps(chunk)
                        yield f"data: {data}\n\n"
                    else:
                        data = json.dumps({"type": "message", "content": str(chunk)})
                        yield f"data: {data}\n\n"
                        
                # Send end-of-stream marker
                yield f"data: {json.dumps({'type': 'end'})}\n\n"
                
            except Exception as e:
                error_data = json.dumps({
                    "type": "error", 
                    "content": f"Stream processing failed: {str(e)}"
                })
                yield f"data: {error_data}\n\n"
        
        return StreamingResponse(
            generate_stream(),
            media_type="text/plain",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Content-Type": "text/event-stream"
            }
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Stream setup failed: {str(e)}")


@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """Global exception handler"""
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal server error",
            "detail": str(exc),
            "path": str(request.url)
        }
    )


def run_server(host: str = "0.0.0.0", port: int = 8000, reload: bool = False):
    """
    Run the FastAPI server
    
    Args:
        host: Host to bind to
        port: Port to bind to
        reload: Enable auto-reload for development
    """
    uvicorn.run(
        "fastapi_chat_service:app",
        host=host,
        port=port,
        reload=reload,
        log_level="info"
    )


if __name__ == "__main__":
    # Run the server
    run_server(host="0.0.0.0", port=8000, reload=True)
