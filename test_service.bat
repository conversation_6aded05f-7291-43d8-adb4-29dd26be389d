@echo off
chcp 65001 >nul
echo ========================================
echo AutoGen Studio Chat API 测试脚本
echo ========================================
echo.

REM 设置基础URL
set BASE_URL=http://localhost:8000

echo [INFO] 开始测试 AutoGen Studio Chat API...
echo [INFO] 基础URL: %BASE_URL%
echo.

REM 测试1: 根端点 - 获取API基本信息
echo ========================================
echo 测试1: 获取API基本信息 (GET /)
echo ========================================
curl -X GET "%BASE_URL%/" ^
     -H "Content-Type: application/json" ^
     -w "\n状态码: %%{http_code}\n响应时间: %%{time_total}s\n" ^
     -s
echo.
pause

REM 测试2: 健康检查
echo ========================================
echo 测试2: 健康检查 (GET /health)
echo ========================================
curl -X GET "%BASE_URL%/health" ^
     -H "Content-Type: application/json" ^
     -w "\n状态码: %%{http_code}\n响应时间: %%{time_total}s\n" ^
     -s
echo.
pause

REM 测试3: 非流式聊天 - 基本对话
echo ========================================
echo 测试3: 非流式聊天 - 基本对话 (POST /chat)
echo ========================================
curl -X POST "%BASE_URL%/chat" ^
     -H "Content-Type: application/json" ^
     -d "{\"messages\":[{\"type\":\"text\",\"content\":\"你好，请介绍一下你自己\",\"role\":\"user\"}],\"model_name\":\"gpt-3.5-turbo\",\"model_type\":\"openai\",\"api_key\":\"your-api-key\",\"openai_base_url\":\"https://api.openai.com/v1\",\"stream\":false}" ^
     -w "\n状态码: %%{http_code}\n响应时间: %%{time_total}s\n" ^
     -s
echo.
pause

REM 测试4: 非流式聊天 - 机器学习问题
echo ========================================
echo 测试4: 非流式聊天 - 机器学习问题
echo ========================================
curl -X POST "%BASE_URL%/chat" ^
     -H "Content-Type: application/json" ^
     -d "{\"messages\":[{\"type\":\"text\",\"content\":\"解释一下机器学习的基本概念\",\"role\":\"user\"}],\"model_name\":\"gpt-3.5-turbo\",\"model_type\":\"openai\",\"api_key\":\"your-api-key\",\"openai_base_url\":\"https://api.openai.com/v1\",\"stream\":false}" ^
     -w "\n状态码: %%{http_code}\n响应时间: %%{time_total}s\n" ^
     -s
echo.
pause

REM 测试5: 非流式聊天 - 带API密钥
echo ========================================
echo 测试5: 非流式聊天 - 带API密钥
echo ========================================
curl -X POST "%BASE_URL%/chat" ^
     -H "Content-Type: application/json" ^
     -d "{\"messages\":[{\"type\":\"text\",\"content\":\"写一个Python函数来计算斐波那契数列\",\"role\":\"user\"}],\"model_name\":\"gpt-3.5-turbo\",\"model_type\":\"openai\",\"api_key\":\"your-api-key\",\"openai_base_url\":\"https://api.openai.com/v1\",\"stream\":false}" ^
     -w "\n状态码: %%{http_code}\n响应时间: %%{time_total}s\n" ^
     -s
echo.
pause

REM 测试6: 非流式聊天 - Ollama模型
echo ========================================
echo 测试6: 非流式聊天 - Ollama模型
echo ========================================
curl -X POST "%BASE_URL%/chat" ^
     -H "Content-Type: application/json" ^
     -d "{\"messages\":[{\"type\":\"text\",\"content\":\"你好世界\",\"role\":\"user\"}],\"model_name\":\"llama2\",\"model_type\":\"ollama\",\"openai_base_url\":\"http://localhost:11434\",\"stream\":false}" ^
     -w "\n状态码: %%{http_code}\n响应时间: %%{time_total}s\n" ^
     -s
echo.
pause

REM 测试7: 流式聊天 - 基本对话
echo ========================================
echo 测试7: 流式聊天 - 基本对话 (POST /chat/stream)
echo ========================================
echo [INFO] 注意: 流式响应将显示Server-Sent Events格式
curl -X POST "%BASE_URL%/chat/stream" ^
     -H "Content-Type: application/json" ^
     -d "{\"messages\":[{\"type\":\"text\",\"content\":\"写一首关于春天的诗\",\"role\":\"user\"}],\"model_name\":\"gpt-3.5-turbo\",\"model_type\":\"openai\",\"api_key\":\"your-api-key\",\"openai_base_url\":\"https://api.openai.com/v1\",\"stream\":true}" ^
     -w "\n状态码: %%{http_code}\n响应时间: %%{time_total}s\n" ^
     -s
echo.
pause

REM 测试8: 流式聊天 - 讲笑话
echo ========================================
echo 测试8: 流式聊天 - 讲笑话
echo ========================================
curl -X POST "%BASE_URL%/chat/stream" ^
     -H "Content-Type: application/json" ^
     -d "{\"messages\":[{\"type\":\"text\",\"content\":\"讲个笑话\",\"role\":\"user\"}],\"model_name\":\"gpt-3.5-turbo\",\"model_type\":\"openai\",\"api_key\":\"your-api-key\",\"openai_base_url\":\"https://api.openai.com/v1\",\"stream\":true}" ^
     -w "\n状态码: %%{http_code}\n响应时间: %%{time_total}s\n" ^
     -s
echo.
pause

REM 测试9: 多轮对话测试
echo ========================================
echo 测试9: 多轮对话测试
echo ========================================
curl -X POST "%BASE_URL%/chat" ^
     -H "Content-Type: application/json" ^
     -d "{\"messages\":[{\"type\":\"text\",\"content\":\"我想学习编程\",\"role\":\"user\"},{\"type\":\"text\",\"content\":\"很好！编程是一项很有用的技能。你想学习哪种编程语言呢？\",\"role\":\"assistant\"},{\"type\":\"text\",\"content\":\"我想学Python\",\"role\":\"user\"}],\"model_name\":\"gpt-3.5-turbo\",\"model_type\":\"openai\",\"api_key\":\"your-api-key\",\"openai_base_url\":\"https://api.openai.com/v1\",\"stream\":false}" ^
     -w "\n状态码: %%{http_code}\n响应时间: %%{time_total}s\n" ^
     -s
echo.
pause

REM 测试10: 带MCP服务器URL的请求
echo ========================================
echo 测试10: 带MCP服务器URL的请求
echo ========================================
curl -X POST "%BASE_URL%/chat" ^
     -H "Content-Type: application/json" ^
     -d "{\"messages\":[{\"type\":\"text\",\"content\":\"测试MCP功能\",\"role\":\"user\"}],\"model_name\":\"gpt-3.5-turbo\",\"model_type\":\"openai\",\"api_key\":\"your-api-key\",\"openai_base_url\":\"https://api.openai.com/v1\",\"mcp_url_list\":[\"http://localhost:3000\"],\"stream\":false}" ^
     -w "\n状态码: %%{http_code}\n响应时间: %%{time_total}s\n" ^
     -s
echo.
pause

REM 测试11: 带团队ID的请求
echo ========================================
echo 测试11: 带团队ID的请求
echo ========================================
curl -X POST "%BASE_URL%/chat" ^
     -H "Content-Type: application/json" ^
     -d "{\"messages\":[{\"type\":\"text\",\"content\":\"团队协作测试\",\"role\":\"user\"}],\"model_name\":\"gpt-3.5-turbo\",\"model_type\":\"openai\",\"api_key\":\"your-api-key\",\"openai_base_url\":\"https://api.openai.com/v1\",\"team_id\":123,\"stream\":false}" ^
     -w "\n状态码: %%{http_code}\n响应时间: %%{time_total}s\n" ^
     -s
echo.
pause

REM 测试12: 错误测试 - 无效的模型类型
echo ========================================
echo 测试12: 错误测试 - 无效的模型类型
echo ========================================
curl -X POST "%BASE_URL%/chat" ^
     -H "Content-Type: application/json" ^
     -d "{\"messages\":[{\"type\":\"text\",\"content\":\"测试错误处理\",\"role\":\"user\"}],\"model_name\":\"gpt-3.5-turbo\",\"model_type\":\"invalid_type\",\"openai_base_url\":\"https://api.openai.com/v1\",\"stream\":false}" ^
     -w "\n状态码: %%{http_code}\n响应时间: %%{time_total}s\n" ^
     -s
echo.
pause

REM 测试13: 错误测试 - 缺少必需参数
echo ========================================
echo 测试13: 错误测试 - 缺少必需参数
echo ========================================
curl -X POST "%BASE_URL%/chat" ^
     -H "Content-Type: application/json" ^
     -d "{\"messages\":[{\"type\":\"text\",\"content\":\"测试缺少参数\",\"role\":\"user\"}]}" ^
     -w "\n状态码: %%{http_code}\n响应时间: %%{time_total}s\n" ^
     -s
echo.
pause

REM 测试14: 错误测试 - 无效的JSON格式
echo ========================================
echo 测试14: 错误测试 - 无效的JSON格式
echo ========================================
curl -X POST "%BASE_URL%/chat" ^
     -H "Content-Type: application/json" ^
     -d "{invalid json}" ^
     -w "\n状态码: %%{http_code}\n响应时间: %%{time_total}s\n" ^
     -s
echo.
pause

REM 测试15: 图片消息测试
echo ========================================
echo 测试15: 图片消息测试
echo ========================================
curl -X POST "%BASE_URL%/chat" ^
     -H "Content-Type: application/json" ^
     -d "{\"messages\":[{\"type\":\"image\",\"content\":\"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k=\",\"role\":\"user\"}],\"model_name\":\"gpt-4-vision-preview\",\"model_type\":\"openai\",\"api_key\":\"your-api-key\",\"openai_base_url\":\"https://api.openai.com/v1\",\"stream\":false}" ^
     -w "\n状态码: %%{http_code}\n响应时间: %%{time_total}s\n" ^
     -s
echo.
pause

echo ========================================
echo 测试完成！
echo ========================================
echo [INFO] 所有测试用例已执行完毕
echo [INFO] 请检查上述响应结果和状态码
echo.
echo 测试用例说明:
echo 1-2:   基础功能测试 (根端点、健康检查)
echo 3-6:   非流式聊天测试 (不同模型和参数)
echo 7-8:   流式聊天测试
echo 9:     多轮对话测试
echo 10-11: 高级功能测试 (MCP、团队ID)
echo 12-14: 错误处理测试
echo 15:    图片消息测试
echo.
pause
